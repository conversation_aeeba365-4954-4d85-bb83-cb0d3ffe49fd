package cc.unitmesh.sketch

import com.intellij.openapi.util.IconLoader
import com.intellij.ui.AnimatedIcon
import javax.swing.Icon

object AutoDevIcons {
    @JvmField
    val AI_COPILOT: Icon = IconLoader.getIcon("/icons/ai-copilot.svg", AutoDevIcons::class.java)

    @JvmField
    val DARK: Icon = IconLoader.getIcon("/icons/autodev-dark.svg", AutoDevIcons::class.java)

    @JvmField
    val AUTODEV_ERROR: Icon = IconLoader.getIcon("/icons/autodev-error.svg", AutoDevIcons::class.java)

    @JvmField
    val COMMAND: Icon = IconLoader.getIcon("/icons/devins-command.svg", AutoDevIcons::class.java)

    @JvmField
    val LOADING = AnimatedIcon.Default()

    @JvmField
    val SEND: Icon = IconLoader.getIcon("/icons/send.svg", AutoDevIcons::class.java)

    @JvmField
    val INSERT: Icon = IconLoader.getIcon("/icons/insert-code.svg", AutoDevIcons::class.java)

    @JvmField
    val RUN: Icon = IconLoader.getIcon("/icons/run.svg", AutoDevIcons::class.java)

    @JvmField
    val ERROR: Icon = IconLoader.getIcon("/icons/error.svg", AutoDevIcons::class.java)

    @JvmField
    val CHECKED: Icon = IconLoader.getIcon("/icons/checked.svg", AutoDevIcons::class.java)

    @JvmField
    val REPAIR: Icon = IconLoader.getIcon("/icons/repair.svg", AutoDevIcons::class.java)

    @JvmField
    val BUILD: Icon = IconLoader.getIcon("/icons/build.svg", AutoDevIcons::class.java)

    @JvmField
    val COPY: Icon = IconLoader.getIcon("/icons/copy.svg", AutoDevIcons::class.java)

    @JvmField
    val IDEA: Icon = IconLoader.getIcon("/icons/idea.svg", AutoDevIcons::class.java)

    @JvmField
    val VIEW: Icon = IconLoader.getIcon("/icons/view.svg", AutoDevIcons::class.java)

    @JvmField
    val TERMINAL: Icon = IconLoader.getIcon("/icons/terminal.svg", AutoDevIcons::class.java)

    @JvmField
    val SAVE_FILE: Icon = IconLoader.getIcon("/icons/save-file.svg", AutoDevIcons::class.java)

    @JvmField
    val STOP: Icon = IconLoader.getIcon("/icons/stop.svg", AutoDevIcons::class.java)

    @JvmField
    val CLEAR: Icon = IconLoader.getIcon("/icons/clear.svg", AutoDevIcons::class.java)

    @JvmField
    val TOOLCHAIN: Icon = IconLoader.getIcon("/icons/toolchain.svg", AutoDevIcons::class.java)

    @JvmField
    val REVIEWER: Icon = IconLoader.getIcon("/icons/reviewer.svg", AutoDevIcons::class.java)

    @JvmField
    val PLANNER: Icon = IconLoader.getIcon("/icons/planner.svg", AutoDevIcons::class.java)

    @JvmField
    val EDIT_TASK: Icon = IconLoader.getIcon("/icons/edit-task.svg", AutoDevIcons::class.java)

    @JvmField
    val RULE: Icon = IconLoader.getIcon("/icons/rule.svg", AutoDevIcons::class.java)

    @JvmField
    val MCP: Icon = IconLoader.getIcon("/icons/mcp.svg", AutoDevIcons::class.java)

    @JvmField
    val CHECK: Icon = IconLoader.getIcon("/icons/check.svg", AutoDevIcons::class.java)

    @JvmField
    val INPUT: Icon = IconLoader.getIcon("/icons/input.svg", AutoDevIcons::class.java)

    @JvmField
    val GIT_NEW: Icon = IconLoader.getIcon("/icons/git-new.svg", AutoDevIcons::class.java)

    @JvmField
    val GIT_DELETE: Icon = IconLoader.getIcon("/icons/git-delete.svg", AutoDevIcons::class.java)

    @JvmField
    val GIT_EDIT: Icon = IconLoader.getIcon("/icons/git-edit.svg", AutoDevIcons::class.java)

    @JvmField
    val GIT_MOVE: Icon = IconLoader.getIcon("/icons/git-move.svg", AutoDevIcons::class.java)

    @JvmField
    val RERUN: Icon = IconLoader.getIcon("/icons/rerun.svg", AutoDevIcons::class.java)

    @JvmField
    val MAGIC: Icon = IconLoader.getIcon("/icons/magic.svg", AutoDevIcons::class.java)

    @JvmField
    val CLOUD_AGENT: Icon = IconLoader.getIcon("/icons/cloud-agent.svg", AutoDevIcons::class.java)

    @JvmField
    val LOCAL_AGENT: Icon = IconLoader.getIcon("/icons/local-agent.svg", AutoDevIcons::class.java)

    @JvmField
    val VARIABLE: Icon = IconLoader.getIcon("/icons/variable.svg", AutoDevIcons::class.java)

    @JvmField
    val HISTORY: Icon = IconLoader.getIcon("/icons/history.svg", AutoDevIcons::class.java)

    @JvmField
    val GITHUB_ISSUE: Icon = IconLoader.getIcon("/icons/github-issue.svg", AutoDevIcons::class.java)
}
