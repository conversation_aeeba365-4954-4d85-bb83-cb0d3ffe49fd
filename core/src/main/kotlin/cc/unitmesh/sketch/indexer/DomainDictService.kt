package cc.unitmesh.sketch.indexer

import cc.unitmesh.sketch.mcp.host.readText
import cc.unitmesh.sketch.settings.coder.coderSetting
import com.intellij.openapi.application.runReadAction
import com.intellij.openapi.components.Service
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.guessProjectDir

@Service(Service.Level.PROJECT)
class DomainDictService(val project: Project) {
    private val baseDir get() = project.coderSetting.state.teamPromptsDir
    private val basePromptDir get() = project.guessProjectDir()?.findChild(baseDir)

    fun loadContent(): String? {
        val promptsDir = basePromptDir ?: return null
        val dictFile = promptsDir.findChild("domain.csv") ?: return null
        return runReadAction { dictFile.readText() }
    }
}