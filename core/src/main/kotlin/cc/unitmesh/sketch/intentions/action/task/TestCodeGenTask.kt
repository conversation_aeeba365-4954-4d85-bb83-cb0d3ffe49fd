package cc.unitmesh.sketch.intentions.action.task

import cc.unitmesh.sketch.AutoDevBundle
import cc.unitmesh.sketch.AutoDevNotifications
import cc.unitmesh.sketch.agent.custom.CustomAgentExecutor
import cc.unitmesh.sketch.settings.customize.customizeSetting
import cc.unitmesh.sketch.agent.custom.model.CustomAgentConfig
import cc.unitmesh.sketch.context.modifier.CodeModifierProvider
import cc.unitmesh.sketch.custom.CustomExtContext
import cc.unitmesh.sketch.gui.chat.message.ChatActionType
import cc.unitmesh.sketch.intentions.action.test.TestCodeGenContext
import cc.unitmesh.sketch.intentions.action.test.TestCodeGenRequest
import cc.unitmesh.sketch.llms.LlmFactory
import cc.unitmesh.sketch.provider.AutoTestService
import cc.unitmesh.sketch.provider.context.*
import cc.unitmesh.sketch.statusbar.AutoDevStatus
import cc.unitmesh.sketch.statusbar.AutoDevStatusService
import cc.unitmesh.sketch.template.GENIUS_CODE
import cc.unitmesh.sketch.template.TemplateRender
import cc.unitmesh.sketch.util.parser.MarkdownCodeHelper
import com.intellij.lang.LanguageCommenters
import com.intellij.openapi.application.*
import com.intellij.openapi.command.WriteCommandAction
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.editor.ScrollType
import com.intellij.openapi.fileEditor.FileEditor
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.progress.ProgressIndicator
import com.intellij.openapi.progress.ProcessCanceledException
import com.intellij.openapi.progress.Task
import com.intellij.openapi.project.DumbService
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiFile
import com.intellij.psi.PsiNameIdentifierOwner
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.runBlocking

class TestCodeGenTask(val request: TestCodeGenRequest, displayMessage: String) :
    Task.Backgroundable(request.project, displayMessage) {
    private val logger = logger<TestCodeGenTask>()
    private val actionType = ChatActionType.GENERATE_TEST
    private val lang = request.file.language.displayName

    private val commenter = LanguageCommenters.INSTANCE.forLanguage(request.file.language) ?: null
    private val comment = commenter?.lineCommentPrefix ?: "//"

    private val templateRender = TemplateRender(GENIUS_CODE)
    private val template = templateRender.getTemplate("test-gen.vm")

    override fun run(indicator: ProgressIndicator) {
        val autoTestService = AutoTestService.context(request.element) ?: return

        indicator.isIndeterminate = false
        indicator.fraction = 0.1
        indicator.text = AutoDevBundle.message("intentions.chat.code.test.step.prepare-context")

        indicator.checkCanceled()

        AutoDevStatusService.notifyApplication(AutoDevStatus.InProgress)
        val testContext = autoTestService.findOrCreateTestFile(request.file, request.project, request.element)
        DumbService.getInstance(request.project).waitForSmartMode()

        indicator.checkCanceled()

        if (testContext == null) {
            AutoDevStatusService.notifyApplication(AutoDevStatus.Error)
            logger.error("Failed to create test file for: ${request.file}")
            return
        }

        indicator.text = AutoDevBundle.message("intentions.chat.code.test.step.collect-context")
        indicator.fraction = 0.3

        indicator.checkCanceled()

        val testPromptContext = TestCodeGenContext()

        val creationContext =
            ChatCreationContext(ChatOrigin.Intention, actionType, request.file, listOf(), element = request.element)

        val contextItems: List<ChatContextItem> = runBlocking {
            // Check for cancellation in the blocking context
            if (indicator.isCanceled) {
                throw ProcessCanceledException()
            }
            ChatContextProvider.collectChatContextList(request.project, creationContext)
        }

        testPromptContext.frameworkContext = contextItems.joinToString("\n", transform = ChatContextItem::text)
        indicator.checkCanceled()

        ReadAction.compute<Unit, Throwable> {
            if (testContext.relatedClasses.isNotEmpty()) {
                testPromptContext.relatedClasses = testContext.relatedClasses.joinToString("\n") {
                    it.format()
                }.lines().joinToString("\n") {
                    "$comment $it"
                }
            }

            testPromptContext.currentClass = runReadAction { testContext.currentObject }?.lines()?.joinToString("\n") {
                "$comment $it"
            } ?: ""
        }

        testPromptContext.imports = testContext.imports.joinToString("\n") {
            "$comment $it"
        }

        testPromptContext.sourceCode = runReadAction {
            when (request.element) {
                is PsiFile -> {
                    request.element.text ?: ""
                }

                !is PsiNameIdentifierOwner -> {
                    testContext.testElement?.text ?: ""
                }

                else -> {
                    request.element.text ?: ""
                }
            }
        }

        testPromptContext.isNewFile = testContext.isNewFile
        testPromptContext.extContext = getCustomAgentTestContext(testPromptContext)

        // Check for cancellation before template rendering
        indicator.checkCanceled()

        templateRender.context = testPromptContext
        val prompter = templateRender.renderTemplate(template)

        logger.info("Prompt: $prompter")

        indicator.fraction = 0.6
        indicator.text = AutoDevBundle.message("intentions.request.background.process.title")

        // Check for cancellation before LLM request
        indicator.checkCanceled()

        val flow: Flow<String> = try {
            LlmFactory.create(request.project).stream(prompter, "", false)
        } catch (e: Exception) {
            AutoDevStatusService.notifyApplication(AutoDevStatus.Error)
            logger.error("Failed to create LLM for: $lang", e)
            return
        }

        runBlocking {
            // Check for cancellation before writing to file
            if (indicator.isCanceled) {
                throw ProcessCanceledException()
            }

            writeTestToFile(request.project, flow, testContext, indicator)

            // Check for cancellation before verification
            if (indicator.isCanceled) {
                throw ProcessCanceledException()
            }

            indicator.fraction = 1.0
            indicator.text = AutoDevBundle.message("intentions.chat.code.test.verify")

            try {
                autoTestService.collectSyntaxError(testContext.outputFile, request.project) {
                    // Check for cancellation before fixing syntax errors
                    if (indicator.isCanceled) {
                        throw ProcessCanceledException()
                    }

                    autoTestService.tryFixSyntaxError(testContext.outputFile, request.project, it)

                    if (it.isNotEmpty()) {
                        AutoDevNotifications.warn(
                            request.project,
                            "Test has error, skip auto run test: ${it.joinToString("\n")}"
                        )
                        indicator.fraction = 1.0
                    } else {
                        // Check for cancellation before running tests
                        if (!indicator.isCanceled) {
                            autoTestService.runFile(request.project, testContext.outputFile, testContext.testElement, false)
                        }
                    }
                }
            } catch (e: ProcessCanceledException) {
                // Re-throw cancellation exception
                throw e
            } catch (e: Exception) {
                AutoDevStatusService.notifyApplication(AutoDevStatus.Ready)
                indicator.fraction = 1.0
            }

            AutoDevStatusService.notifyApplication(AutoDevStatus.Ready)
            indicator.fraction = 1.0
        }
    }

    override fun onThrowable(error: Throwable) {
        super.onThrowable(error)
        AutoDevStatusService.notifyApplication(AutoDevStatus.Error)
        AutoDevNotifications.error(project, "Failed to generate test: ${error.message}")
    }

    private suspend fun writeTestToFile(
        project: Project,
        flow: Flow<String>,
        context: TestFileContext,
        indicator: ProgressIndicator
    ) {
        val fileEditorManager = FileEditorManager.getInstance(project)
        var editors: Array<FileEditor> = emptyArray()
        ApplicationManager.getApplication().invokeAndWait {
            editors = fileEditorManager.openFile(context.outputFile, true)
            fileEditorManager.setSelectedEditor(context.outputFile, "text-editor")
        }

        val isTargetEmpty = context.outputFile.length == 0L
        if (editors.isNotEmpty() && (context.isNewFile || isTargetEmpty)) {
            val suggestion = StringBuilder()
            val editor = fileEditorManager.selectedTextEditor

            flow.collect {
                // Check for cancellation during flow collection
                if (indicator.isCanceled) {
                    throw ProcessCanceledException()
                }

                suggestion.append(it)
                val codeBlocks = MarkdownCodeHelper.parseCodeFromString(suggestion.toString())
                codeBlocks.forEach {
                    WriteCommandAction.writeCommandAction(project).compute<Any, RuntimeException> {
                        editor?.document?.replaceString(0, editor.document.textLength, it)
                        editor?.caretModel?.moveToOffset(editor.document.textLength)
                        editor?.scrollingModel?.scrollToCaret(ScrollType.RELATIVE)
                    }
                }
            }

            logger.info("LLM suggestion: $suggestion")
            return
        }

        val suggestion = StringBuilder()
        flow.collect {
            // Check for cancellation during flow collection
            if (indicator.isCanceled) {
                throw ProcessCanceledException()
            }
            suggestion.append(it)
        }

        logger.info("LLM suggestion: $suggestion")

        val modifier = CodeModifierProvider().modifier(context.language)
            ?: throw IllegalStateException("Unsupported language: ${context.language}")

        val codeBlocks = MarkdownCodeHelper.parseCodeFromString(suggestion.toString())
        codeBlocks.forEach {
            modifier.insertTestCode(context.outputFile, project, it)
        }
    }

    private fun getCustomAgentTestContext(testPromptContext: TestCodeGenContext): String {
        if (!project.customizeSetting.enableCustomAgent) return ""

        val agent = loadTestRagConfig() ?: return ""

        val query = testPromptContext.sourceCode
        val stringFlow: Flow<String> = CustomAgentExecutor(project)
            .execute(query, agent, StringBuilder()) ?: return ""

        val responseBuilder = StringBuilder()
        runBlocking {
            stringFlow.collect { string ->
                responseBuilder.append(string)
            }
        }

        return responseBuilder.toString()
    }

    private fun loadTestRagConfig(): CustomAgentConfig? {
        val rags = CustomAgentConfig.loadFromProject(project)
        if (rags.isEmpty()) return null

        return rags.firstOrNull { it.name == CustomExtContext.TestContext.agentName }
    }
}